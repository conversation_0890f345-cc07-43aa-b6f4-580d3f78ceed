import { fetchData } from '../offer-helper';
import { TcbPromotionCard } from '../tcb-promotion-card';
import {
  ID_PROMOTION_FILTER,
  ID_PROMOTION_SEARCH,
  TYPE_PROMOTION_SEARCH,
  TYPE_PROMOTION_FILTER,
} from '../constants/offer-common';
import DOMPurify from 'dompurify';

export function renderViewCard(type) {
  const handlers = {
    [TYPE_PROMOTION_SEARCH]: () => {
      const element = document.getElementById(ID_PROMOTION_SEARCH);
      if (element) {
        element.handleValueSearch();
      }
    },
    [TYPE_PROMOTION_FILTER]: () => {
      const element = document.getElementById(ID_PROMOTION_FILTER);
      if (element) {
        const delay = 100;
        setTimeout(() => {
          element.handleParamFilter();
        }, delay);
      }
    },
  };

  let currentType = TYPE_PROMOTION_FILTER;
  if (type === TYPE_PROMOTION_SEARCH) {
    currentType = TYPE_PROMOTION_SEARCH;
  }
  const handler = handlers[currentType];
  if (handler) {
    handler();
  }
}

export function getDataResultPage(params) {
  const { dataUrl, classGroupCardName, dataExpiryDate, isCardlabel, totalPromotionData } = params;
  let emptyHTML = false;
  if (dataUrl.includes('offset=0')) {
    emptyHTML = true;
  }

  fetchData(dataUrl)
    .then((response) => {
      const promotionData = response;
      const total = Number(promotionData.total || 0);

      if (typeof totalPromotionData === 'function') {
        totalPromotionData(total);
      }

      if (total > 0 && Array.isArray(promotionData.results)) {
        renderTcbCard(promotionData.results, classGroupCardName, dataExpiryDate, isCardlabel, emptyHTML);
      }
    })
    .catch((_) => {
      if (typeof totalPromotionData === 'function') {
        totalPromotionData(0);
      }
    });
}

export function renderTcbCard(response, classGroupCardName, dataExpiryDate, isCardlabel, emptyHTML) {
  const cardPromotionList = $(classGroupCardName);

  if (!cardPromotionList.length) {
    return;
  }

  const newCard = cardPromotionList.attr('class').includes('new-group-card');
  let element = '';
  response.forEach((item) => {
    element += renderPromotionCard(item, isCardlabel, newCard, dataExpiryDate);
  });
  if (emptyHTML) {
    cardPromotionList.empty();
  }
  cardPromotionList.append(DOMPurify.sanitize(element));
  const cardInstance = new TcbPromotionCard();
  cardInstance.init();
}

export function renderPromotionCard(item, cardLabel = true, newCard = true, dataExpiryDate = {}) {
  const groupCardLabelMobile = getGroupCardLabelMobile(item, cardLabel);
  const groupCardLabel = getGroupCardLabel(item, cardLabel);
  const { thumbnail, partner, url, description, expiryDate, favIconClass } = getItemDetails(item);
  const { lang, dayText, labelExpired, labelExpiredCountDown } = getExpiryData(dataExpiryDate);
  let newCardClass = '';
  if (newCard) {
    newCardClass = 'new-card';
  }

  return `
    <div class="card ${newCardClass}">
      ${groupCardLabelMobile}
      <a href="${url}" class="tcb-card__link"></a>
      <div class="tcb-card__group_card">
        <div class="card__image">
          <img
            data-offer-check-image
            class="tcb-thumbnail"
            src="${thumbnail}"
            alt="${partner}"
          />
          <div class="fav-icon ${favIconClass}">
            <img
              src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/fav-icon-2.svg"
              alt="fav-icon.svg"
            />
          </div>
        </div>
        <div class="group_card__content">
          <div class="tcb-card__content card__content">
            ${groupCardLabel}
            <div class="card__title">${partner}</div>
            <div class="card__description">${description}</div>
            <div
              class="card__date"
              data-lang="${lang}"
              data-day-text="${dayText}"
              data-label-expired="${labelExpired}"
              data-label-expired-count-down="${labelExpiredCountDown}"
              data-promotion-card-expired="${expiryDate}"
            >
              <div class="progress-bar-container">
                <div class="progress-bar"></div>
              </div>
              <img class="icon-expiry-date" src="/etc.clientlibs/techcombank/clientlibs/clientlib-site/resources/images/expired_clock.svg" alt="expired_clock" />
              <span></span>
            </div>
          </div>
        </div>
      </div>
    </div>
  `;
}

function getItemDetails(item) {
  let thumbnail = '',
    partner = '',
    url = '',
    description = '',
    expiryDate = '',
    favIconClass = 'display-none';

  if (!item) {
    return { thumbnail, partner, url, description, expiryDate, favIconClass };
  }

  if (item.thumbnail) {
    thumbnail = item.thumbnail;
  }

  if (Array.isArray(item.partner)) {
    partner = item.partner.join(', ');
  }

  if (item.url) {
    url = item.url;
  }

  if (item.description) {
    description = item.description;
  }

  if (item.expiryDate) {
    expiryDate = formatDateToDDMMYYYY(item.expiryDate);
  }

  if (item.favourite === 'true') {
    favIconClass = 'display-unset';
  }

  return { thumbnail, partner, url, description, expiryDate, favIconClass };
}

function getGroupCardLabelMobile(item, cardLabel) {
  if (!cardLabel || !item || !item.products || !item.products.length) {
    return '';
  }

  let mobileLabels = '';
  for (const product of item.products) {
    mobileLabels += `<div class="card__label-mobile">${product.trim()}</div>`;
  }
  return `<div class="group-card__label-mobile">${mobileLabels}</div>`;
}

function getGroupCardLabel(item, cardLabel) {
  if (!cardLabel || !item || !item.products || !item.products.length) {
    return '<div class="group-card__label"></div>';
  }

  let desktopLabels = '';
  for (let i = 0; i < item.products.length; i++) {
    if (i > 0) {
      desktopLabels += ', ';
    }
    desktopLabels += item.products[i].trim();
  }
  return `<div class="group-card__label"><div class="card__label">${desktopLabels}</div></div>`;
}

function getExpiryData(dataExpiryDate) {
  let lang = '',
    dayText = '',
    labelExpired = '',
    labelExpiredCountDown = '';

  if (dataExpiryDate) {
    if (dataExpiryDate.lang) {
      lang = dataExpiryDate.lang;
    }
    if (dataExpiryDate.dayText) {
      dayText = dataExpiryDate.dayText;
    }
    if (dataExpiryDate.labelExpired) {
      labelExpired = dataExpiryDate.labelExpired;
    }
    if (dataExpiryDate.labelExpiredCountDown) {
      labelExpiredCountDown = dataExpiryDate.labelExpiredCountDown;
    }
  }

  return { lang, dayText, labelExpired, labelExpiredCountDown };
}

function formatDateToDDMMYYYY(input) {
  const maxLengthPad = 2;
  let d;

  if (typeof input === 'string' && input.includes('/')) {
    const [day, month, year] = input.split('/');
    d = new Date(year, month - 1, day);
  } else {
    d = new Date(input);
  }

  const day = String(d.getDate()).padStart(maxLengthPad, '0');
  const month = String(d.getMonth() + 1).padStart(maxLengthPad, '0');
  const year = d.getFullYear();

  return `${day}/${month}/${year}`;
}
