import { BaseComponent } from './base';
import { fetchData } from '../scripts/offer-helper';
import DOMPurify from 'dompurify';
import { screenSmMax } from './constants/common';
import { CLASS_HIDDEN_ELEMENT, KEY_RECENT_SEARCH, TYPE_PROMOTION_SEARCH } from './constants/offer-common';
import { renderViewCard } from './utils/get-data-result-page.util';
import { StorageUtils } from './utils/storage.util';
import { debounce } from 'lodash';
import { setURLParams, removeURLParams, getQueryParam } from './utils/params.util';

export class TcbNeedPromotionSearch extends BaseComponent {
  constructor() {
    super();
    this.bindMethods();
    const delayTime = 700;
    this.debouncedSearch = debounce(this.fetchSearchResults, delayTime);
    this.url = this.dataset.url;
    this.contentWishList = this.dataset.notfoundWishlist;
    this.baseUrl = new URL(window.location);
    this.CLASS_POPUP_MOBILE = 'popup-search-mobile';
    this.CLASS_SHOW_POPUP = 'show-popup';
    this.comonObject = {
      keyParam: 'search-with-merchant',
      keyPayload: 'searchWithMerchant',
      value: '',
    };
  }

  bindMethods() {
    [
      'handleEnter',
      'isMobileView',
      'updateBorderRadius',
      'togglePromotionInfo',
      'toggleRemoveIcon',
      'clearInputValue',
      'handleInputFocus',
      'handleInputBlur',
      'handleInput',
      'handleDropdownMouseDown',
      'handleDocumentClick',
      'handleWindowResize',
      'renderRecentSearch',
      'handleDropdownItemClick',
      'fetchSearchResults',
    ].forEach((method) => {
      this[method] = this[method].bind(this);
    });
  }

  createEvent(eventName) {
    return new Event(eventName, { bubbles: true });
  }

  connectedCallback() {
    this.searchBar = this.querySelector('.tcb-need-promotion-search');
    if (!this.searchBar) {
      return;
    }
    this.cacheDOMElements();
    this.addEventListeners();
    this.updateBorderRadius();
    this.toggleRemoveIcon();
    this.togglePromotionInfo();
    this.renderRecentSearch();

    const searchTextValueDecoded = getQueryParam('q');
    if (searchTextValueDecoded) {
      this.elementInputSearch.value = searchTextValueDecoded;
      const delay = 10;
      setTimeout(() => {
        this.elementInputSearch.dispatchEvent(this.createEvent('input'));
        const searchWithMerchant = getQueryParam(this.comonObject.keyParam);
        if (searchWithMerchant) {
          this.comonObject.value = searchWithMerchant;
        }
        this.buttonSearch.dispatchEvent(this.createEvent('click'));
      }, delay);
    }
  }

  disconnectedCallback() {
    this.removeEventListeners();
  }

  cacheDOMElements() {
    this.elementInputSearch = this.searchBar.querySelector('.need-search-input');
    this.inputDropdown = this.searchBar.querySelector('.need-input-dropdown');
    this.searchContainer = this.searchBar.querySelector('.need-search-container');
    this.buttonSearch = this.searchBar.querySelector('.need-promotions-filter');
    this.iconRemoveSearch = this.searchBar.querySelector('.need-icon-remove-search');
    this.inforPromotion = this.searchBar.querySelector('.need-infor-search');
    this.resultSearch = this.searchBar.querySelector('.need-group-promotion-dropdown.result-search');
    this.elementRecentSearch = this.searchBar.querySelector('.render-recent-search');
  }

  addEventListeners() {
    if (this.buttonSearch) {
      this.buttonSearch.addEventListener('click', () => this.setParamSearchTextInUrl());
    }
    if (this.iconRemoveSearch) {
      this.iconRemoveSearch.addEventListener('click', this.clearInputValue);
    }
    if (this.elementInputSearch) {
      this.elementInputSearch.addEventListener('keydown', this.handleEnter);
      this.elementInputSearch.addEventListener('focus', this.handleInputFocus);
      this.elementInputSearch.addEventListener('blur', this.handleInputBlur);
      this.elementInputSearch.addEventListener('input', this.handleInput);
    }
    if (this.inputDropdown) {
      this.inputDropdown.addEventListener('mousedown', this.handleDropdownMouseDown);
      this.inputDropdown.addEventListener('click', this.handleDropdownItemClick);
    }
    document.addEventListener('click', this.handleDocumentClick);
    window.addEventListener('resize', this.handleWindowResize);
  }

  removeEventListeners() {
    if (this.iconRemoveSearch) {
      this.iconRemoveSearch.removeEventListener('click', this.clearInputValue);
    }
    if (this.elementInputSearch) {
      this.elementInputSearch.removeEventListener('keydown', this.handleEnter);
      this.elementInputSearch.removeEventListener('focus', this.handleInputFocus);
      this.elementInputSearch.removeEventListener('blur', this.handleInputBlur);
      this.elementInputSearch.removeEventListener('input', this.handleInput);
    }
    if (this.inputDropdown) {
      this.inputDropdown.removeEventListener('mousedown', this.handleDropdownMouseDown);
      this.inputDropdown.removeEventListener('click', this.handleDropdownItemClick);
    }
    document.removeEventListener('click', this.handleDocumentClick);
    window.removeEventListener('resize', this.handleWindowResize);
  }

  isMobileView() {
    return window.innerWidth <= screenSmMax;
  }

  updateBorderRadius() {
    if (!this.elementInputSearch || !this.searchContainer || !this.buttonSearch) {
      return;
    }

    const isFocused = document.activeElement === this.elementInputSearch;
    if (isFocused) {
      this.searchContainer.style.borderBottomLeftRadius = 'unset';
      this.searchContainer.style.borderBottomRightRadius = 'unset';
      this.buttonSearch.style.borderBottomRightRadius = 'unset';
    } else {
      this.searchContainer.style.borderBottomLeftRadius = '';
      this.searchContainer.style.borderBottomRightRadius = '';
      this.buttonSearch.style.borderBottomRightRadius = '';
    }
  }

  togglePromotionInfo() {
    if (!this.elementInputSearch || !this.inforPromotion || !this.resultSearch) {
      return;
    }

    const hasValue = this.elementInputSearch.value.trim() !== '';
    if (hasValue) {
      this.inforPromotion.classList.add(CLASS_HIDDEN_ELEMENT);
      this.resultSearch.classList.remove(CLASS_HIDDEN_ELEMENT);
    } else {
      this.inforPromotion.classList.remove(CLASS_HIDDEN_ELEMENT);
      this.resultSearch.classList.add(CLASS_HIDDEN_ELEMENT);
    }
  }

  toggleRemoveIcon() {
    if (!this.elementInputSearch || !this.iconRemoveSearch) {
      return;
    }

    const hasValue = this.elementInputSearch.value.trim() !== '';
    if (hasValue) {
      this.iconRemoveSearch.classList.remove(CLASS_HIDDEN_ELEMENT);
    } else {
      this.iconRemoveSearch.classList.add(CLASS_HIDDEN_ELEMENT);
    }

    this.togglePromotionInfo();
  }

  clearInputValue(e) {
    e.stopPropagation();
    if (!this.elementInputSearch) {
      return;
    }

    this.elementInputSearch.value = '';
    this.toggleRemoveIcon();
    this.searchBar.classList.remove(this.CLASS_POPUP_MOBILE);
    document.body.classList.remove(this.CLASS_SHOW_POPUP);
    if (this.inputDropdown) {
      this.inputDropdown.classList.add(CLASS_HIDDEN_ELEMENT);
    }
    this.updateBorderRadius();
    this.setParamSearchTextInUrl();
  }

  handleEnter(event) {
    const keyCodeEnter = 13;
    if (event.keyCode === keyCodeEnter) {
      if (this.searchBar) {
        this.searchBar.classList.remove(this.CLASS_POPUP_MOBILE);
      }
      document.body.classList.remove(this.CLASS_SHOW_POPUP);
      if (this.inputDropdown) {
        this.inputDropdown.classList.add(CLASS_HIDDEN_ELEMENT);
      }
      this.elementInputSearch.blur();
      this.updateBorderRadius();
      this.setParamSearchTextInUrl();
    }
  }

  handleInputFocus() {
    if (!this.inputDropdown) {
      return;
    }
    this.renderRecentSearch();
    if (this.isMobileView()) {
      this.searchBar.classList.add(this.CLASS_POPUP_MOBILE);
      document.body.classList.add(this.CLASS_SHOW_POPUP);
    }

    this.inputDropdown.classList.remove(CLASS_HIDDEN_ELEMENT);
    this.updateBorderRadius();
    this.togglePromotionInfo();
  }

  handleInputBlur() {
    if (this.isMobileView()) {
      return;
    }

    const delayTime = 100;
    setTimeout(() => {
      if (!this.contains(document.activeElement)) {
        this.searchBar.classList.remove(this.CLASS_POPUP_MOBILE);
        if (this.inputDropdown) {
          this.inputDropdown.classList.add(CLASS_HIDDEN_ELEMENT);
        }
        this.updateBorderRadius();
      }
    }, delayTime);
  }

  handleInput() {
    this.comonObject.value = '';
    this.toggleRemoveIcon();
    const valueInput = this.elementInputSearch.value.trim();
    if (this.resultSearch) {
      this.resultSearch.parentElement.classList.add(CLASS_HIDDEN_ELEMENT);
    }
    this.debouncedSearch(valueInput);
    if (valueInput === '') {
      this.setParamSearchTextInUrl();
    }
  }

  handleDropdownMouseDown(e) {
    e.preventDefault();
  }

  handleDocumentClick(e) {
    if (!this.contains(e.target)) {
      if (this.inputDropdown) {
        this.inputDropdown.classList.add(CLASS_HIDDEN_ELEMENT);
      }
      this.searchBar.classList.remove(this.CLASS_POPUP_MOBILE);
      document.body.classList.remove(this.CLASS_SHOW_POPUP);
      this.updateBorderRadius();
    }
  }

  handleWindowResize() {
    this.updateBorderRadius();
  }

  renderRecentSearch() {
    if (!this.elementRecentSearch) {
      return;
    }

    const recentSearch = StorageUtils.get(KEY_RECENT_SEARCH) || [];
    if (!recentSearch.length) {
      this.elementRecentSearch.classList.add(CLASS_HIDDEN_ELEMENT);
      return;
    }

    this.elementRecentSearch.classList.remove(CLASS_HIDDEN_ELEMENT);
    const html = recentSearch
      .map((item) => {
        return `<div class="need-input-dropdown-item" data-value="${item}">${item}</div>`;
      })
      .join('');

    const dropdownList = this.elementRecentSearch.querySelector('.need-input-dropdown-list');
    if (dropdownList) {
      dropdownList.innerHTML = DOMPurify.sanitize(html);
    }
  }

  handleDropdownItemClick(e) {
    const target = e.target.closest('.need-input-dropdown-item');
    if (!target || !this.elementRecentSearch || !this.elementInputSearch || !this.iconRemoveSearch) {
      return;
    }

    const value = target.textContent.trim();
    if (value === '') {
      return;
    }

    this.elementInputSearch.value = value;
    this.elementInputSearch.blur();
    this.iconRemoveSearch.classList.remove(CLASS_HIDDEN_ELEMENT);

    const isInPopularBrand = target.closest('.popular-brand') || target.closest('.result-search');
    if (isInPopularBrand) {
      const dataName = target.getAttribute('data-name');
      this.comonObject.value = dataName;
      this.debouncedSearch(dataName);
      return;
    }

    this.elementInputSearch.dispatchEvent(this.createEvent('input'));
    this.iconRemoveSearch.classList.remove(CLASS_HIDDEN_ELEMENT);
  }

  fetchSearchResults() {
    const searchText = this.elementInputSearch.value || '';
    const params = new URLSearchParams({
      limit: 0,
      offset: 0,
      isSuggest: true,
      searchText: searchText.toLowerCase(),
      sort: 'most popular',
    });

    if (this.comonObject.value) {
      params.set(this.comonObject.keyPayload, this.comonObject.value);
    }

    const api = `${this.url}.getsuggestmerchants.json?${params.toString()}`;

    fetchData(api)
      .then((response) => {
        if (response) {
          this.renderSuggestions(response);
        } else {
          this.renderSuggestions([]);
        }
      })
      .catch(() => {
        this.renderSuggestions([]);
      });
  }

  renderSuggestions(data) {
    if (document.activeElement === this.elementInputSearch) {
      this.resultSearch.parentElement.classList.remove(CLASS_HIDDEN_ELEMENT);
    }

    const suggestionContainer = this.resultSearch;
    const keyword = this.elementInputSearch.value.trim();
    const titleTypeBrand = suggestionContainer.querySelector('.need-tilte-type-brand');
    const titleTypeBrandNotWishlist = suggestionContainer.querySelector('.need-tilte-type-brand.not-wishlist');
    const dropdownList = suggestionContainer.querySelector('.need-input-dropdown-list');

    titleTypeBrand.classList.add(CLASS_HIDDEN_ELEMENT);
    dropdownList.classList.add(CLASS_HIDDEN_ELEMENT);

    const dataKeys = Object.keys(data);

    if (!dataKeys.length) {
      const htmlNotFound = this.contentWishList + ` “${keyword}”`;
      if (titleTypeBrandNotWishlist) {
        titleTypeBrandNotWishlist.innerHTML = DOMPurify.sanitize(htmlNotFound);
        titleTypeBrandNotWishlist.classList.remove(CLASS_HIDDEN_ELEMENT);
      }
      return;
    }

    if (titleTypeBrandNotWishlist) {
      titleTypeBrandNotWishlist.classList.add(CLASS_HIDDEN_ELEMENT);
    }

    titleTypeBrand.classList.remove(CLASS_HIDDEN_ELEMENT);
    dropdownList.classList.remove(CLASS_HIDDEN_ELEMENT);

    const escapeRegExpPattern = /[.*+?^${}()|[\]\\]/g;
    const escapeRegExpReplacement = '\\$&';
    const escapedKeyword = keyword.replace(escapeRegExpPattern, escapeRegExpReplacement);
    const regex = new RegExp(`(${escapedKeyword})`, 'gi');

    const listWish = dataKeys
      .map((key) => {
        const displayText = data[key];
        const highlighted = displayText.replace(regex, '<strong>$1</strong>');
        return `<div class="need-input-dropdown-item" data-name="${key}">${highlighted}</div>`;
      })
      .join('');

    dropdownList.innerHTML = DOMPurify.sanitize(listWish);
  }

  setParamSearchTextInUrl() {
    const valueInput = this.elementInputSearch.value;
    if (valueInput.length) {
      setURLParams('q', valueInput, true);
      const commonValue = this.comonObject.value;
      if (commonValue) {
        setURLParams(this.comonObject.keyParam, commonValue, true);
      } else {
        removeURLParams(this.comonObject.keyParam, true);
      }
    } else {
      removeURLParams('q', true);
      removeURLParams(this.comonObject.keyParam, true);
    }
    renderViewCard(TYPE_PROMOTION_SEARCH);
  }
}
