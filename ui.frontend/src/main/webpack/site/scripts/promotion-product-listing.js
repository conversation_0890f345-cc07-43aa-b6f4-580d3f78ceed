import { BaseComponent } from './base';
import { getDataResultPage } from './utils/get-data-result-page.util';
import { getEndpoint } from './utils/promotion-generate-endpoint.util';
import { DEFAULT_SORT_PARAMS } from './constants/offer-common';

export class TcbPromotionProductList extends BaseComponent {
  constructor() {
    super();
    this.CLASS_TAB_SELECT = 'active';
    this.PARAM_PROMOTION_LIST = {};

    this.paramsValue = {
      limit: 4,
      offset: 0,
      sort: DEFAULT_SORT_PARAMS,
    };

    this.typesTilter = {
      cardTypes: 'card-types',
      products: 'products',
      types: 'types',
    };
    this.init();
  }

  init() {
    this.root = this.querySelector('.tcb-promotion-product-list');
    if (!this.root) {
      return;
    }
    const dataset = this.dataset;
    this.dataUrl = dataset.url;

    this.dataExpiryDate = {
      lang: dataset.lang || '',
      dayText: dataset.dayText || '',
      labelExpired: dataset.labelExpired || '',
      labelExpiredCountDown: dataset.labelExpiredCountDown || '',
    };

    this.cacheDOMElements();
    this.bindEvents();
    this.renderProductList();
  }

  cacheDOMElements() {
    this.tabCardItem = this.root.querySelectorAll('.tab-card__link');
    this.classGroupCardName = this.root.querySelector('.group-card');
  }

  bindEvents() {
    if (this.tabCardItem) {
      this.tabCardItem.forEach((button) => {
        button.addEventListener('click', (e) => {
          this.handleButtonClick(e);
        });
      });
    }
  }

  handleButtonClick(e) {
    const clickedButton = e.currentTarget;
    const param = clickedButton.getAttribute('data-param');
    const value = clickedButton.getAttribute('data-value');

    if (!param || !value) {
      return;
    }

    if (clickedButton.classList.contains(this.CLASS_TAB_SELECT)) {
      clickedButton.classList.remove(this.CLASS_TAB_SELECT);
      this.removeParamValue(param, value);
    } else {
      clickedButton.classList.add(this.CLASS_TAB_SELECT);
      this.addParamValue(param, value);
    }

    this.renderProductList();
  }

  addParamValue(param, value) {
    if (!this.PARAM_PROMOTION_LIST[param]) {
      this.PARAM_PROMOTION_LIST[param] = [];
    }
    if (!this.PARAM_PROMOTION_LIST[param].includes(value)) {
      this.PARAM_PROMOTION_LIST[param].push(value);
    }
  }

  removeParamValue(param, value) {
    if (!this.PARAM_PROMOTION_LIST[param]) {
      return;
    }

    const index = this.PARAM_PROMOTION_LIST[param].indexOf(value);
    if (index !== -1) {
      this.PARAM_PROMOTION_LIST[param].splice(index, 1);
    }

    if (this.PARAM_PROMOTION_LIST[param].length === 0) {
      delete this.PARAM_PROMOTION_LIST[param];
    }
  }

  renderProductList() {
    const baseUrl = getEndpoint(this.dataUrl, this.paramsValue);
    const promotionParams = this.getParamPromotionListAsQuery();
    let fullUrl = baseUrl;

    if (promotionParams) {
      if (baseUrl.includes('?')) {
        fullUrl = baseUrl + '&' + promotionParams;
      } else {
        fullUrl = baseUrl + '?' + promotionParams;
      }
    }

    getDataResultPage({
      dataUrl: fullUrl,
      classGroupCardName: this.classGroupCardName,
      dataExpiryDate: this.dataExpiryDate,
      isCardlabel: false,
    });
  }

  getParamPromotionListAsQuery() {
    const params = [];

    for (const [key, values] of Object.entries(this.PARAM_PROMOTION_LIST)) {
      if (!values || values.length === 0) {
        continue;
      }

      if (key === this.typesTilter.cardTypes) {
        params.push(`${key}=${values.join(',')}`);
      } else {
        const encodedValues = values.map((v) => encodeURIComponent(v)).join(',');
        params.push(`${encodeURIComponent(key)}=${encodedValues}`);
      }
    }

    return params.join('&');
  }
}
