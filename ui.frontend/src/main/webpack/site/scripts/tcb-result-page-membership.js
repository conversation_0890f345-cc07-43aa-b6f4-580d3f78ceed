import { BaseComponent } from './base';
import { getQueryParam } from './utils/params.util';
import { CLASS_HIDDEN_ELEMENT } from './constants/offer-common';

export class TcbResultPageMembership extends BaseComponent {
  constructor() {
    super();
    this.init();
  }

  init() {
    this.isExpanded = false;
    
    const DEFAULT_MAX_INITIAL_CARDS = 3;
    const promotionCard = this.querySelector('.tcb-promotion-card');
    this.maxInitialCards =
      (promotionCard &&
      promotionCard.getAttribute('data-promotion-membership-view-more-item')) ||
      DEFAULT_MAX_INITIAL_CARDS;
    // Check if URL has any filter parameters
    const hasUrlParams = this.hasUrlParameters();

    if (hasUrlParams) {
      // Apply all URL parameter filtering when component initializes
      this.applyAllFilters();
      // Initialize card visibility and view more functionality
      this.initializeCardVisibility();
      this.setupViewMoreButton();
      this.showCardContainer();
      // Update count display after initialization
      this.updateCountDisplay();
    } else {
      // No URL parameters - hide card container and show no data message
      this.hideCardContainer();
      // Set count to 0 when no filters are active
      this.updateCountDisplay();
    }
  }

  /**
   * Check if URL has any filter parameters
   */
  hasUrlParameters() {
    const cardTypes = getQueryParam('card-types');
    const locations = getQueryParam('locations');
    const types = getQueryParam('types');
    const memberships = getQueryParam('memberships');
    const products = getQueryParam('products');
    const partners = getQueryParam('partners');
    const searchQuery = getQueryParam('q');

    return !!(cardTypes || locations || types || memberships || products || partners || searchQuery);
  }

  /**
   * Parse date string in ISO 8601 format (YYYY-MM-DDThh:mm:ss.sss±hh:mm) to Date object
   * Used for data-published-date
   */
  parsePublishedDate(dateString) {
    if (!dateString) {
      return new Date(0);
    } // Return epoch for invalid dates
    // Parse ISO 8601 format directly
    const date = new Date(dateString);
    // Check if date is valid
    if (isNaN(date.getTime())) {
      return new Date(0);
    }
    return date;
  }

  /**
   * Parse date string in DD/MM/YYYY format to Date object
   * Used for data-promotion-card-expired
   */
  parseExpiredDate(dateString) {
    if (!dateString) {
      return new Date(0);
    } // Return epoch for invalid dates
    const parts = dateString.split('/');
    if (parts.length !== 3) {
      return new Date(0);
    }
    // Create date: new Date(year, month-1, day)
    return new Date(parseInt(parts[2]), parseInt(parts[1]) - 1, parseInt(parts[0]));
  }

  /**
   * Sort cards based on URL sort parameter
   */
  sortCards(cards) {
    const sortParam = getQueryParam('sort');
    const cardsArray = Array.from(cards);

    switch (sortParam) {
      case 'latest':
        // Sort by data-published-date descending
        return cardsArray.sort((a, b) => {
          const dateA = this.parsePublishedDate(a.getAttribute('data-published-date'));
          const dateB = this.parsePublishedDate(b.getAttribute('data-published-date'));
          return dateB - dateA; // Descending order
        });

      case 'expiring-soon':
        // Sort by data-promotion-card-expired ascending (earliest expiry first)
        return cardsArray.sort((a, b) => {
          const elementA = a.querySelector('[data-promotion-card-expired]');
          const elementB = b.querySelector('[data-promotion-card-expired]');
          let expiredValueA = null;
          let expiredValueB = null;
          if (elementA) {
            expiredValueA = elementA.getAttribute('data-promotion-card-expired');
          }
          if (elementB) {
            expiredValueB = elementB.getAttribute('data-promotion-card-expired');
          }
          const expiredA = this.parseExpiredDate(expiredValueA);
          const expiredB = this.parseExpiredDate(expiredValueB);
          return expiredA - expiredB; // Ascending order
        });

      case 'more-time-to-consider':
        // Sort by data-promotion-card-expired descending (latest expiry first)
        return cardsArray.sort((a, b) => {
          const elementA = a.querySelector('[data-promotion-card-expired]');
          const elementB = b.querySelector('[data-promotion-card-expired]');
          let expiredValueA = null;
          let expiredValueB = null;
          if (elementA) {
            expiredValueA = elementA.getAttribute('data-promotion-card-expired');
          }
          if (elementB) {
            expiredValueB = elementB.getAttribute('data-promotion-card-expired');
          }
          const expiredA = this.parseExpiredDate(expiredValueA);
          const expiredB = this.parseExpiredDate(expiredValueB);
          return expiredB - expiredA; // Descending order
        });

      default:
        // Default sort: data-fav=true first, then data-published-date desc
        return cardsArray.sort((a, b) => {
          const favA = a.getAttribute('data-fav') === 'true';
          const favB = b.getAttribute('data-fav') === 'true';

          // First sort by favorite status (true first)
          if (favA !== favB) {
            return favB - favA; // true (1) comes before false (0)
          }

          // If same favorite status, sort by published date descending
          const dateA = this.parsePublishedDate(a.getAttribute('data-published-date'));
          const dateB = this.parsePublishedDate(b.getAttribute('data-published-date'));
          return dateB - dateA;
        });
    }
  }

  /**
   * Parse URL parameter and return array of values
   * Handles URL encoding: spaces as '+' and commas as '%2C'
   */
  parseUrlParameter(paramName) {
    const decodedParam = getQueryParam(paramName);

    if (!decodedParam) {
      return [];
    }

    // Split by comma and trim whitespace
    return decodedParam
      .split(',')
      .map((value) => value.trim())
      .filter((value) => value.length);
  }

  /**
   * Parse the memberships URL parameter and return array of membership types
   * Handles URL encoding: spaces as '+' and commas as '%2C'
   */
  parseUrlMemberships() {
    const values = this.parseUrlParameter('memberships');
    // Convert spaces to hyphens for membership values
    return values.map((membership) => membership.replace(/\s+/g, '-'));
  }

  /**
   * Get all elements with specific data attribute
   */
  getElementsWithDataAttribute(attributeName) {
    return this.querySelectorAll(`[data-${attributeName}]`);
  }

  /**
   * Get all elements with data-membership attributes
   */
  getMembershipElements() {
    return this.getElementsWithDataAttribute('membership');
  }

  /**
   * Check if an element's data attribute matches any of the target values
   */
  elementMatchesDataAttribute(element, attributeName, targetValues) {
    const elementData = element.getAttribute(`data-${attributeName}`);

    if (!elementData || !targetValues.length) {
      return false;
    }

    // Split element data by comma and trim
    const elementDataList = elementData.split(',').map((value) => value.trim());

    // Check if any target value matches any element data value
    return targetValues.some((targetValue) => elementDataList.includes(targetValue));
  }

  /**
   * Check if an element's membership data matches any of the target memberships
   */
  elementMatchesMemberships(element, targetMemberships) {
    return this.elementMatchesDataAttribute(element, 'membership', targetMemberships);
  }

  /**
   * Check if an element's description matches the search query
   */
  elementMatchesSearchQuery(element, searchQuery) {
    if (!searchQuery) {
      return true; // No search query means all elements match
    }

    const descriptionElement = element.querySelector('.card__description');
    if (!descriptionElement) {
      return false; // No description element found
    }

    const descriptionText = descriptionElement.textContent || descriptionElement.innerText || '';
    // Case-insensitive search
    return descriptionText.toLowerCase().includes(searchQuery.toLowerCase());
  }

  /**
   * Get all card elements that have any filterable data attributes or descriptions
   */
  getAllFilterableElements() {
    // Return all cards since we now also search by description
    return this.getAllCards();
  }

  /**
   * Show all elements (no filtering)
   */
  showAllElements() {
    const elements = this.getAllFilterableElements();
    elements.forEach((element) => {
      element.classList.remove(CLASS_HIDDEN_ELEMENT);
    });
    // Refresh card visibility after filtering changes
    this.refreshCardVisibility();
  }

  /**
   * Check if element matches all active filters
   */
  elementMatchesAllFilters(element, filters) {
    // Check search query first (if present)
    const searchQuery = getQueryParam('q');
    if (searchQuery && !this.elementMatchesSearchQuery(element, searchQuery)) {
      return false;
    }

    // If no other filters are active, show the element (search query already checked above)
    if (Object.keys(filters).length === 0) {
      return true;
    }

    // Element must match ALL active filters
    for (const [attributeName, targetValues] of Object.entries(filters)) {
      if (targetValues.length > 0) {
        if (!this.elementMatchesDataAttribute(element, attributeName, targetValues)) {
          return false;
        }
      }
    }

    return true;
  }

  /**
   * Get all cards that pass the current filters (not hidden by filtering)
   */
  getFilteredCards() {
    const allCards = this.getAllCards();
    const filters = this.getCurrentFilters();

    // Filter cards based on criteria
    const filteredCards = Array.from(allCards).filter((card) => {
      return this.elementMatchesAllFilters(card, filters);
    });

    // Sort the filtered cards
    return this.sortCards(filteredCards);
  }

  /**
   * Get current active filters from URL parameters
   */
  getCurrentFilters() {
    const filters = {};

    // Parse all possible URL parameters
    const cardTypes = this.parseUrlParameter('card-types');
    const locations = this.parseUrlParameter('locations');
    const types = this.parseUrlParameter('types');
    const memberships = this.parseUrlMemberships(); // Special handling for memberships
    const products = this.parseUrlParameter('products');
    const partners = this.parseUrlParameter('partners');

    // Only add filters that have values
    if (cardTypes.length > 0) {
      filters['card-types'] = cardTypes;
    }
    if (locations.length > 0) {
      filters['locations'] = locations;
    }
    if (types.length > 0) {
      filters['types'] = types;
    }
    if (memberships.length > 0) {
      filters['membership'] = memberships;
    }
    if (products.length > 0) {
      filters['products'] = products;
    }
    if (partners.length > 0) {
      filters['partners'] = partners;
    }
    return filters;
  }

  /**
   * Reorder cards in the DOM based on sorted array
   */
  reorderCardsInDOM(sortedCards) {
    const cardContainer = this.querySelector('.group-card');
    if (!cardContainer) {
      return;
    }

    // Remove all cards from DOM
    sortedCards.forEach((card) => {
      card.remove();
    });

    // Re-append cards in sorted order
    sortedCards.forEach((card) => {
      cardContainer.appendChild(card);
    });
  }

  /**
   * Apply all filters to elements
   */
  applyFiltersToElements(filters) {
    const elements = this.getAllFilterableElements();

    elements.forEach((element) => {
      if (this.elementMatchesAllFilters(element, filters)) {
        // Show matching elements - remove filter-based hiding
        element.classList.remove(CLASS_HIDDEN_ELEMENT);
      } else {
        // Hide non-matching elements
        element.classList.add(CLASS_HIDDEN_ELEMENT);
      }
    });

    // Get filtered and sorted cards, then reorder them in DOM
    const filteredAndSortedCards = this.getFilteredCards();
    this.reorderCardsInDOM(filteredAndSortedCards);

    // Refresh card visibility after filtering changes
    this.refreshCardVisibility();
  }

  /**
   * Main method to apply all filtering based on URL parameters
   */
  applyAllFilters() {
    const filters = this.getCurrentFilters();
    this.applyFiltersToElements(filters);
    // Update count display after applying filters
    this.updateCountDisplay();
  }

  /**
   * Get all card elements within the component
   */
  getAllCards() {
    return this.querySelectorAll('.tcb-card__group .card');
  }
  /**
   * Get the view more button element
   */
  getViewMoreButton() {
    return this.querySelector('.tcb-result-page-membership__view-more');
  }

  /**
   * Get the card container element
   */
  getCardContainer() {
    return this.querySelector('tcb-promotion-card');
  }
  
  /**
   * Count the number of filtered cards that match current filters
   */
  countFilteredCards() {
    // If no URL parameters are present, return 0
    if (!this.hasUrlParameters()) {
      return 0;
    }
    const filteredCards = this.getFilteredCards();
    return filteredCards.length;
  }

  /**
   * Update the count display in the count-filter-membership element
   */
  updateCountDisplay() {
    const countElement = this.querySelector('[data-count-filter-membership]');
    if (countElement) {
      const count = this.countFilteredCards();
      countElement.setAttribute('data-count-filter-membership', count.toString());
    }
  }

  /**
   * Show the card container
   */
  showCardContainer() {
    const cardContainer = this.getCardContainer();
    if (cardContainer) {
      cardContainer.classList.remove(CLASS_HIDDEN_ELEMENT);
    }
  }

  /**
   * Hide the card container
   */
  hideCardContainer() {
    const cardContainer = this.getCardContainer();
    const viewMoreButton = this.getViewMoreButton();
    const cardMembershipTitle = this.querySelector('.tcb-result-page-membership__title');
    if (cardContainer) {
      cardContainer.classList.add(CLASS_HIDDEN_ELEMENT);
    }
    if (viewMoreButton) {
      viewMoreButton.classList.add(CLASS_HIDDEN_ELEMENT);
    }
    if (cardMembershipTitle){
      cardMembershipTitle.classList.add(CLASS_HIDDEN_ELEMENT);
    }
  }

  /**
   * Initialize card visibility - show first 3 filtered cards, hide the rest
   */
  initializeCardVisibility() {
    const filteredCards = this.getFilteredCards();
    const viewMoreButton = this.getViewMoreButton();

    if (filteredCards.length <= this.maxInitialCards) {
      if (viewMoreButton) {
        viewMoreButton.classList.add(CLASS_HIDDEN_ELEMENT);
      }
      return;
    }

    // Hide filtered cards beyond the initial limit
    filteredCards.forEach((card, index) => {
      if (index >= this.maxInitialCards) {
        card.classList.add(CLASS_HIDDEN_ELEMENT);
      }
    });

    // Show the view more button
    if (viewMoreButton) {
      viewMoreButton.classList.remove(CLASS_HIDDEN_ELEMENT);
    }
  }

  /**
   * Setup click event listener for the view more button
   */
  setupViewMoreButton() {
    const viewMoreButton = this.getViewMoreButton();
    if (!viewMoreButton) {
      return;
    }

    viewMoreButton.addEventListener('click', () => {
      this.toggleCardVisibility();
    });
  }

  /**
   * Toggle between showing more cards and showing less cards
   */
  toggleCardVisibility() {
    const viewMoreButton = this.getViewMoreButton();

    if (this.isExpanded) {
      // Reset to initial state - re-apply all filters with pagination
      this.applyAllFilters();
      this.isExpanded = false;
    } else {
      // Show all filtered cards without pagination limit
      const filteredCards = this.getFilteredCards();

      // Show all filtered cards
      filteredCards.forEach((card) => {
        card.classList.remove(CLASS_HIDDEN_ELEMENT);
      });

      this.isExpanded = true;
      if (viewMoreButton) {
        viewMoreButton.classList.add(CLASS_HIDDEN_ELEMENT);
      }
      // Update count display when expanding
      this.updateCountDisplay();
    }
  }

  /**
   * Re-initialize card visibility after filtering changes
   */
  refreshCardVisibility() {
    // Reset expanded state
    this.isExpanded = false;

    // Get all cards that pass the current filters
    const filteredCards = this.getFilteredCards();
    const viewMoreButton = this.getViewMoreButton();

    // If we have fewer filtered cards than the initial limit, hide the view more button
    if (filteredCards.length <= this.maxInitialCards) {
      if (viewMoreButton) {
        viewMoreButton.classList.add(CLASS_HIDDEN_ELEMENT);
      }
      // Update count display even when returning early
      this.updateCountDisplay();
      return;
    }

    // Apply pagination to filtered cards: show first maxInitialCards, hide the rest
    filteredCards.forEach((card, index) => {
      if (index >= this.maxInitialCards) {
        card.classList.add(CLASS_HIDDEN_ELEMENT);
      }
    });

    // Show the view more button
    if (viewMoreButton) {
      viewMoreButton.classList.remove(CLASS_HIDDEN_ELEMENT);
    }

    // Update count display after refreshing card visibility
    this.updateCountDisplay();
  }
}
