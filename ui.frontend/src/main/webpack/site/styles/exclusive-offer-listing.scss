:root {
  --secondary-grey-70: #313131;
}

.header-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.875rem;

  .title {
    font-size: 2rem;
  }

  .btn-see-more {
    background-color: var(--primary-black);
    padding: 1rem 1.5rem;
    border-radius: 0.5rem;
    color: var(--primary-white);
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 0.875rem;

    @include xs {
      display: none;
    }
  }
}

.card-see-more {
  text-align: center;

  div {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    margin-top: 2rem;
    cursor: pointer;

    span {
      font-weight: bold;
    }
  }
}

.tab-card {
  align-items: center;
  gap: 1.5rem;
  margin-bottom: 1.5rem;
  border-bottom: 0.063rem solid var(--secondary-mid-grey-60);
  display: -webkit-box;
  -webkit-box-align: center;
  -ms-flex-align: center;
  -webkit-box-flex: 1;
  -ms-flex-positive: 1;
  flex-grow: 1;
  -ms-flex-wrap: nowrap;
  flex-wrap: nowrap;
  overflow-x: scroll;

  &::-webkit-scrollbar {
    display: none;
  }

  .tab-card__item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 1rem;
    line-height: 1.5rem;
    cursor: pointer;

    .tab-card__link {
      color: var(--secondary-mid-grey-100);
      font-weight: 400;
      padding-bottom: 0.75rem;

      &.active {
        font-weight: 600;
        color: var(--secondary-grey-70);
        position: relative;

        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 0.25rem;
          background-color: var(--accent);
        }
      }
    }
  }
}

.hide {
  display: none !important;
}

.group-card {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1.5rem;
  .group_card{
    display: flex;
    flex-direction: column;
    height: 100%;
    .group_card__content {
      background: var(--primary-white);
      height: 100%;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
    }
  }
  &.tab-content {
    .card {
      display: none;
    }
  }

  .card {
    background-color: var(--primary-white);
    position: relative;
    border-radius: 0.5rem;
    overflow: hidden;

    .card__link-mobile {
      display: none;
    }

    .fav-icon {
      position: absolute;
      bottom: 0;
      right: 0;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }

  .card__subtitle_mb {
    display: none;
  }

  .card__image {
    img {
      width: 100%;
      height: auto;
      object-fit: cover;
      display: flex;
      aspect-ratio: 1.333;
    }
  }

  .group_card__content {
    padding: 1rem;
  }

  .card__content {
    overflow: hidden;

    .card__title {
      margin-bottom: 0.25rem;
      font-weight: 600;
      font-size: 1rem;
      height: 1.5rem;
      line-height: 1.5rem;
      max-width: max-content;
      white-space: nowrap;
    }

    .card__subtitle {
      font-size: 0.875rem;
      color: var(--accent);
      font-weight: 600;
      margin-bottom: 0.75rem;
      line-height: 1.313rem;
      text-transform: uppercase;
      letter-spacing: 0.125rem;
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .card__description {
      overflow: hidden;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      margin-bottom: 0.75rem;
      height: 3rem;
    }
  }

  .card__date {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    min-height: 1.5rem;
    margin-bottom: 0.75rem;
    span {
      color: var(--secondary-grey-60);
    }
  }
  .card__button {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-weight: 600;
    cursor: pointer;

    &:hover {
      color: var(--primary-red);
    }

    a {
      display: inline-flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 600;

      &:hover {
        color: var(--primary-red);
      }
    }
  }

  .progress-bar-container {
    width: 2rem;
    height: 0.25rem;
    background-color: var(--secondary-mid-grey-60);
    border-radius: 0.25rem;
    overflow: hidden;
    display: none;

    .progress-bar {
      height: 100%;
      background-color: var(--accent);
      width: 0;
      border-radius: 0.313rem;
    }
  }

  @include maxLgSemi {
    grid-template-columns: repeat(3, 1fr);
  }

  @include maxSm2 {
    grid-template-columns: repeat(2, 1fr);
    gap: 0.75rem;
  }

  @include xs {
    grid-template-columns: repeat(1, 1fr);
    gap: 0.375rem;

    .card__button {
      display: none;
    }

    .card__content {
      margin-bottom: 0;
    }

    .card__date {
      font-size: 0.875rem;
      flex-flow: column-reverse;
      width: 85%;
      align-items: baseline !important;

      .progress-bar-container {
        width: 100%;
      }
    }

    .group_card__subtitle_mb {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 0.5rem;
      flex-wrap: wrap;
    }

    .card__subtitle_mb {
      display: block;
      font-size: 0.813rem;
      color: var(--accent);
      font-weight: 600;
      line-height: 1.313rem;
      border: 0.063rem solid var(--secondary-light-grey-80);
      text-transform: uppercase;
      letter-spacing: 0.125rem;
      padding: 0.25rem 0.375rem;
      border-radius: 0.313rem;
    }

    .card__subtitle {
      display: none !important;
    }

    .card {
      padding: 1rem;
      position: relative;

      .card__link-mobile {
        display: block;
        position: absolute;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
      }

      .fav-icon {
        position: absolute;
        bottom: 1rem;
        right: 1rem;
        display: flex;
        align-items: center;
        justify-content: center;
      }
    }

    .group_card__content {
      padding: 0;
    }

    .card__content {
      .card__description {
        margin-bottom: 0.25rem;
      }
    }

    .card .group_card {
      display: flex;
      gap: 0.75rem;
      flex-direction: row;
      height: auto;
      .card__date{
        margin-bottom: 0;
      }
      .card__image {
        width: 7.75rem;

        img {
          border-radius: 0.563rem;
          width: 7.75rem;
        }
      }
    }
  }
}

.marquee {
  animation: scroll-left 12s linear infinite;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0%);
  }

  100% {
    transform: translateX(-100%);
  }
}