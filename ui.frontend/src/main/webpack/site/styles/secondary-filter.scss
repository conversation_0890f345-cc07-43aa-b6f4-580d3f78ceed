.promotion-hub-secondary {
  --icon-color: #1e1e1e;
  --dark-gray: #e3e4e6;

  padding-left: unset !important;
  padding-right: unset !important;
  .toggle-item {
    display: flex;
  }
  .input__checkbox:checked {
    border-color: var(--primary-red);
    background-color: var(--primary-red);
  }
  .checkbox-item__wrapper input {
    margin-right: 0.5rem;
  }

  .offer-listing-promotions {
    &__wrapper {
      display: flex;
      flex-wrap: wrap;
      margin: -0.75rem;

      @include maxLgSemi {
        margin: 0;
      }

      .tcb-modal_action-bar {
        width: 100%;
        display: flex;

        gap: 1rem;
        z-index: 1;
        .tcb-apply-filter-button {
          padding: 1rem 1.5rem;
          display: flex;
          width: 100%;
          background-color: var(--primary-black);
          justify-content: space-between;
          color: var(--primary-white);
          font-weight: 600;
          font-size: 1rem;
          border-radius: 0.5rem;

          &:hover {
            background-color: var(--gray-600);

            img {
              filter: brightness(0) saturate(100%) invert(99%) sepia(1%) saturate(2105%) hue-rotate(297deg)
                brightness(114%) contrast(101%);
            }
          }
        }

        .tcb-close-filter-button {
          width: 100%;
          margin: 0 auto;
          display: flex;
          justify-content: space-between;
          background-color: transparent;
          align-items: center;
          color: var(--primary-black);
          font-weight: 600;
          font-size: 1rem;
          line-height: 1.25;
          gap: 1.5rem;
          padding-left: 1rem;
          padding-right: 1rem;
          cursor: pointer;
          border-radius: 0.5rem;
          border: 0.0625rem solid var(--secondary-gray);
          &.disabled {
            border-color: var(--cta-disabled);
            color: var(--secondary-mid-grey-100);
            pointer-events: none;
          }
          img {
            width: 1rem;
            height: 1rem;
          }
        }
      }

      .offer-cards {
        &__wrapper {
          .pagination {
            width: 100%;
            .pag-container {
              display: flex;
              grid-template-columns: 1.25rem 1fr 1.25rem;
              width: 100%;
              align-items: center;
              justify-content: center;

              .pag-number {
                display: flex;

                &.pag-scroll {
                  overflow: auto;
                  scrollbar-width: none;
                  @include maxSm {
                    width: 60%;
                    scroll-snap-type: x mandatory;
                  }
                }
              }

              label {
                display: flex;
                width: 1.5rem;
                height: 1.5rem;
                align-items: center;
                justify-content: center;

                .next {
                  transform: rotate(180deg);
                }
                img:first-child {
                  display: none;
                }
                img:last-child {
                  display: block;
                }
                &.disable {
                  pointer-events: none;
                  img:first-child {
                    display: block;
                  }
                  img:last-child {
                    display: none;
                  }
                }
              }

              a {
                cursor: inherit;
                scroll-snap-align: start;
              }

              a:not(.active) > span:hover {
                cursor: pointer;
                background-color: rgba(0, 0, 0, 0.04) !important;
                color: unset !important;
                border-radius: 0.25rem !important;
              }

              a.hide {
                display: none !important;
              }

              a.hide-left {
                &:after {
                  content: '...';
                  margin-left: 0.75rem;
                  pointer-events: none;
                }
              }

              a.hide-right {
                &:before {
                  content: '...';
                  margin-right: 0.75rem;
                }
              }
            }
          }
        }
      }

      .mobile-filter-section {
        .dropdown__list {
          max-height: 25.25rem;
        }
      }
    }

    @include maxSm {
      .dropdown {
        &__wrapper {
          margin-bottom: unset;
        }

        &__display {
          padding: 1rem;

          .sort-title {
            display: flex;
            flex-direction: column;

            .display {
              &__title {
                color: var(--secondary-grey-60);
                font-size: 0.75rem;
              }
            }
          }
        }
      }

      .mobile-filter-section {
        .dropdown__list {
          max-height: 20rem;
        }
      }

      .offer-listing-filter__button--cloud {
        gap: 0.5rem;
        width: 100vw;
        scroll-snap-type: x mandatory;
        > * {
          scroll-snap-align: center;
        }
      }
    }

    .membership-checkbox,
    .membership-checkbox-mobile {
      color: var(--secondary-grey-60);
      .offer-filter__checkbox-wrapper {
        margin-bottom: 1.5rem;

        .checkbox-parent {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 0.0625rem solid var(--secondary-mid-grey-60);
          padding-bottom: 0.5rem;
          cursor: pointer;
          .checkbox-item__wrapper {
            .all-checked:checked {
              background-color: var(--primary-black) !important;
            }

            .all-checked:checked::before {
              border-color: var(--primary-white) !important;
            }

            .input__checkbox:checked {
              border-color: var(--primary-red);
              background-color: var(--primary-red);
            }

            .input__checkbox:checked::before {
              border-color: var(--primary-black);
            }
          }
        }

        .checkbox-child {
          .input__checkbox:checked {
            border-color: var(--primary-red);
            background-color: var(--primary-red);
          }
        }

        .toggle-icon:hover {
          cursor: pointer;
        }

        .toggle-icon.expanded {
          display: none;
        }

        .offer-filter__checkbox-child-wrapper {
          flex-direction: column;
          gap: 1rem;
          display: none;

          .checkbox-child {
            margin-bottom: 0;
            margin-top: 1.5rem;
          }
        }
      }
    }

    .product-checkbox,
    .product-checkbox-mobile {
      color: var(--secondary-grey-60);
      .offer-filter__checkbox-wrapper {
        margin-bottom: 1.5rem;

        .checkbox-parent {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 0.0625rem solid var(--secondary-mid-grey-60);
          padding-bottom: 0.5rem;
          cursor: pointer;
          .checkbox-item__wrapper {
            .all-checked:checked {
              background-color: var(--primary-black) !important;
            }

            .all-checked:checked::before {
              border-color: var(--primary-white) !important;
            }

            .input__checkbox:checked {
              border-color: var(--primary-red);
              background-color: var(--primary-red);
            }

            .input__checkbox:checked::before {
              border-color: var(--primary-black);
            }
          }
        }

        .checkbox-child {
          .input__checkbox:checked {
            border-color: var(--primary-red);
            background-color: var(--primary-red);
          }
        }

        .toggle-icon:hover {
          cursor: pointer;
        }

        .toggle-icon.expanded {
          display: none;
        }

        .offer-filter__checkbox-child-wrapper {
          flex-direction: column;
          gap: 1rem;
          display: none;

          .checkbox-child {
            margin-bottom: 0;
            margin-top: 1.5rem;
          }
        }
      }
    }

    .sort-checkbox-mobile {
      color: var(--secondary-grey-60);
      .offer-filter__checkbox-wrapper {
        margin-bottom: 1.5rem;

        .checkbox-parent {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 0.0625rem solid var(--secondary-mid-grey-60);
          padding-bottom: 0.5rem;
          cursor: pointer;
          .checkbox-item__wrapper {
            .all-checked:checked {
              background-color: var(--primary-black) !important;
            }

            .all-checked:checked::before {
              border-color: var(--primary-white) !important;
            }

            .input__checkbox:checked {
              border-color: var(--primary-red);
              background-color: var(--primary-red);
            }

            .input__checkbox:checked::before {
              border-color: var(--primary-black);
            }
          }
        }

        .checkbox-child {
          .input__checkbox:checked {
            border-color: var(--primary-red);
            background-color: var(--primary-red);
          }
        }

        .toggle-icon:hover {
          cursor: pointer;
        }

        .toggle-icon.expanded {
          display: none;
        }

        .offer-filter__checkbox-child-wrapper {
          flex-direction: column;
          gap: 1rem;
          display: none;
          &.analytics-ol-sort {
            .checkbox-item__wrapper {
              color: var(--secondary-grey-60);
              font-weight: 400;
              margin-bottom: 1.5rem;
            }
          }

          .checkbox-child {
            margin-bottom: 0;
            margin-top: 1.5rem;
          }
        }
      }
    }

    .card-checkbox {
      color: var(--secondary-grey-60);
      .offer-filter__checkbox-wrapper {
        margin-bottom: 1.5rem;

        .checkbox-parent {
          display: flex;
          justify-content: space-between;
          align-items: center;
          border-bottom: 0.0625rem solid var(--secondary-mid-grey-60);
          padding-bottom: 0.5rem;
          cursor: pointer;
          .checkbox-item__wrapper {
            .all-checked:checked {
              background-color: var(--primary-black) !important;
            }

            .all-checked:checked::before {
              border-color: var(--primary-white) !important;
            }

            .input__checkbox:checked {
              border-color: var(--primary-red);
              background-color: var(--primary-red);
            }

            .input__checkbox:checked::before {
              border-color: var(--primary-black);
            }
          }
        }

        .checkbox-child {
          .input__checkbox:checked {
            border-color: var(--primary-red);
            background-color: var(--primary-red);
          }
        }

        .toggle-icon:hover {
          cursor: pointer;
        }

        .toggle-icon.expanded {
          display: none;
        }

        .offer-filter__checkbox-child-wrapper {
          flex-direction: column;
          gap: 1rem;
          display: none;
          @include maxLgSemi {
            padding: 0;
          }

          .checkbox-child {
            margin-bottom: 0;
            margin-top: 1.5rem;
          }
        }
      }
    }

    .promotion-total-count {
      margin-bottom: 1.5rem;
    }
  }
  .offer-listing-promotions__wrapper {
    margin: 0;
    flex-wrap: nowrap;
    gap: 3rem;
    @include xs {
      gap: 0;
    }
    .offer-filter__count-container {
      padding: 0;
      margin: 0;
      @include xs {
        justify-content: end;
      }
    }
    .promotion-hub-secondary .offer-listing-promotions .offer-listing-filter__button--cloud {
      @include maxSm {
        padding: 0;
        margin: 0;
      }
    }
  }
  .promotion-hub-filter-card-mobile {
    display: none;
    @include xs {
      display: block;
      position: relative;
    }
    .tcb-filter-mobile {
      position: absolute;
      left: 0;
      top: 0;
      width: auto;
    }
  }
}

.sort-checkbox-mobile {
  .offer-filter__checkbox-wrapper {
    .checkbox-parent {
      margin-bottom: 1.25rem;
    }
  }
}

.offer-filter__header-modal {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}
.checkbox-item__group-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  img {
    width: 2.5rem;
    height: auto;
  }
}
.show-filter {
  display: block !important;
}
[data-card-type='the-tin-dung'],
[data-card-type='the-thanh-toan'] {
  display: none;
}
.offer-listing-promotions {
  &.open {
    .offer-filter__header {
      padding: 1.5rem 1.5rem 1rem;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 0.0625rem solid var(--secondary-mid-grey-60);
      .title {
        font-size: 1rem;
        font-weight: 600;
        line-height: 1.5rem;
      }
    }
    .news_filter-group {
      display: block;
      padding: 1rem 1rem 5.625rem;
      margin-bottom: auto;
    }

    .tcb-modal {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1300;
      background: rgba(0, 0, 0, 0.6);
      overflow: hidden;
      @include maxSm {
        width: 100vw;
      }
    }
    .tcb-modal__wrapper {
      display: flex;
      position: relative;
      width: 100vw;
      height: 80%;
      margin-top: auto;
      background: var(--primary-white);
      overflow: hidden;
      flex-direction: column;
      justify-content: space-between;
      box-shadow: 0 0.25rem 1.875rem 0 #0000004d;
      border-top-left-radius: 1.25rem;
      border-top-right-radius: 1.25rem;
      padding: 0 0 1.5rem;
      -ms-overflow-style: none; /* IE and Edge */
      scrollbar-width: none; /* Firefox */
      &::-webkit-scrollbar {
        display: none;
      }
      .news_filter-group {
        width: 100%;
        height: calc(100% - 10.5rem);
        overflow: hidden auto;
        padding-top: 1.5rem;
      }
    }

    .tcb-modal_action-bar {
      padding: 1.5rem 1.5rem 0 1.5rem;
      margin-top: auto;

      .tcb-button {
        display: flex;
        justify-content: space-between;

        img {
          line-height: 0;
        }
      }
    }

    .tcb-modal {
      display: flex;
    }
  }

  .tcb-modal {
    .news_filter-group {
      display: flex;
      flex-direction: column;
      padding: 0 1.5rem;
      flex: 1;
    }

    .offer-filter__title {
      position: relative;
      margin-bottom: 1rem;
    }
  }

  .offer-filter__container {
    padding: 0;
    width: 25%;
  }

  .offer-filter__title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;

    h6,
    p {
      line-height: 1.25rem;
      font-size: 1rem;
      font-weight: 600;
    }
    &--no-border {
      padding-bottom: 0;
      margin-bottom: 1rem;
    }
    &-collapse {
      cursor: pointer;
      .expanded {
        display: none;
      }
    }
  }
  .offer-filter__title-close {
    display: none;
    width: 1.5rem;
    height: 1.5rem;
  }

  .promotion-offer-filter__title {
    &.offer-filter__title {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 0.75rem;
      img {
        width: 1.5rem;
        height: 1.5rem;
      }
    }
  }

  .offer-filter__checkbox-wrapper {
    margin-bottom: 1.5rem;
    position: relative;

    .checkbox-item__wrapper {
      font-weight: 600;
      color: var(--primary-black);
    }
    .checkbox-child {
      .checkbox-item__wrapper {
        font-weight: 400;
      }
    }
  }

  .offer-cards__wrapper {
    margin-bottom: 3.25rem;
    width: 100%;
    @include xs {
      margin-bottom: 0;
    }
    &.not-offer {
      .read-more {
        display: none;
      }
      .offer-card-list,
      .card-promotion__list {
        display: none;
      }
      .not-found {
        display: block;
      }
    }
  }

  .offer-cards__wrapper:last-child {
    margin-bottom: 0;
  }

  .input__radio {
    background-color: var(--primary-white);
  }

  .section__margin-medium & {
    margin-top: 2rem;
    margin-bottom: 2rem;
  }

  @include lg {
    .section__margin-medium & {
      margin-top: 3rem;
      margin-bottom: 3rem;
    }
  }

  @include maxSm {
    .offer-filter__dropdown {
      margin-bottom: 1.25rem;
      @include xs{
        margin-bottom: 1.5rem;
      }
      &.thinner {
        margin-bottom: 0.5rem;
      }
    }

    .btn-open-filter {
      display: flex;
      font-size: 1rem;
      border: 0;
      width: fit-content;
      font-weight: 400;
      justify-content: space-between;
      align-items: center;
      padding: 0;
      background-color: transparent;
      gap: 1.25rem;
      white-space: nowrap;
      line-height: 1.5rem;

      @include maxSm {
        gap: 0.5rem;
      }

      &.active {
        border: 0.0625rem solid var(--gray-900);
      }

      &.scroll-over {
        width: auto;
        line-height: 0;
        position: fixed;
        top: 6.25rem;
        left: 0;
        background-color: var(--primary-white);
        padding: 1.0625rem 1.75rem 1.0625rem 2.125rem;
        border-radius: 0 0.5rem 0.5rem 0;
        box-shadow:
          0 2.0625rem 11.3125rem rgba(0, 0, 0, 0.04),
          0 0.8617rem 4.7261rem rgba(0, 0, 0, 0.029),
          0 0.4607rem 2.5268rem rgba(0, 0, 0, 0.024),
          0 0.2583rem 1.4165rem rgba(0, 0, 0, 0.02),
          0 0.1372rem 0.7523rem rgba(0, 0, 0, 0.016),
          0 0.0571rem 0.3131rem rgba(0, 0, 0, 0.011);
        display: none;
        z-index: 1300;
      }
    }

    .tcb-filter-button-icon {
      display: flex;
      justify-content: center;
      align-items: center;
      transition: all 0.3s ease-in-out;
    }
  }
}
.promotion-hub-secondary {
  .btn-open-filter__total{
    background: var(--secondary-grey-100);
    color: var(--primary-white);
    width: 1.313rem;
    border-radius: 100%;
    height: 1.313rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.75rem;
  }
  .tcb-filter-mobile {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .tcb-result-filter-mobile {
    display: none;
    z-index: 3;
    @include xs {
      display: flex;
    }
    box-shadow:
      0 0.0569rem 0.3131rem 0 #00000003,
      0 0.1369rem 0.7525rem 0 #00000004,
      0 0.2581rem 1.4163rem 0 #00000005,
      0 0.4606rem 2.5269rem 0 #00000006,
      0 2.0625rem 11.3125rem 0 #0000000a;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 0.75rem 0;
    background-color: var(--primary-white);
    align-items: center;
    justify-content: space-between;
    .tcb-result-filter-content {
      flex-grow: 3;
      position: relative;
      .tcb-filter-modal-sort-bottom {
        display: none;
        position: absolute;
        background-color: var(--primary-white);
        top: 0;
        right: 0;
        transform: translate(0, -110%);
        padding: 1.25rem;
        width: 15.625rem;
        border-radius: 0.75rem;
        &.show {
          display: block;
        }
        ul {
          padding: 0;
          list-style: none;
          li {
            color: var(--secondary-grey-60);
            margin-bottom: 1.5rem;
            &:last-child {
              margin-bottom: 0;
            }
          }
        }
      }
    }
    .offer-listing-filter__button--cloud {
      mask-image: unset;
      width: auto;
      margin: 0;
      padding: 0 !important;
      border-right: 0.0625rem solid var(--secondary-grey-100);
      justify-content: center;
      display: flex;
      align-items: center;
    }
    .tcb-btn-search-bottom {
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .tcb-btn-sort-bottom {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: 0.75rem;
      border-right: 0.0625rem solid var(--secondary-grey-100) !important;
      &.dropdown__display {
        padding: 0;
        border: 0;
        border-radius: 0;
      }
    }
  }
}
