// /*
//  * Global variables: Breakpoints
//  * Defined to make sure that the user interface will display correctly across devices commonly used by consumers
//  */

// Small smartphones (portrait view)
$screen-xs-min: 320px;
$screen-xs-max: 575px;

// Small tablets and large smartphones (landscape view)
$screen-sm-min: 576px;
$screen-sm-max: 767px;

// Small tablets (portrait view)
$screen-md-min: 768px;
$screen-md-semi: 820px;
$screen-md-max: 991px;

// Small tablets Pro(portrait view)
$screen-lg-semi: 1024px;

// Tablets and small desktops
$screen-lg-min: 992px;
$screen-lg-max: 1999px;

// Large tablets and desktops
$screen-xl-min: 1200px;
$screen-xl-max: 1339px;

// Large tablets and desktops
$screen-xxl-min: 1440px;
$screen-xxl-max: 1920px;

// Medium desktop
$screen-xl-semi: 1280px;

/*
 * Device is Mobile phone
 */
@mixin mobile {
  @media (pointer: coarse) {
    @content;
  }
}

/*
 * Device is Tablet
 */
@mixin tablet {
  @media (min-width: #{$screen-xl-min}) and (max-width: #{$screen-xl-max}) {
    @content;
  }
}

@mixin tabletPro {
  @media (min-width: #{$screen-md-max}) and (max-width: #{$screen-xl-min}) {
    @content;
  }
}

@mixin maxTabletPro {
  @media (max-width: #{$screen-xl-min}) {
    @content;
  }
}

/*
 * Device is Desktop
 */
@mixin desktop {
  @media (pointer: fine), (pointer: none) {
    @content;
  }
}

/*
 * Device is Touch Desktop
 */
@mixin touchDesktop {
  @media (pointer: fine) and (any-pointer: coarse) {
    @content;
  }
}

/*
 * Small Mobile phones
 */

@mixin minXs {
  @media (max-width: $screen-xs-min) {
    @content;
  }
}
@mixin xs {
  @media (max-width: $screen-xs-max) {
    @content;
  }
}

// Small devices
@mixin sm {
  @media (min-width: #{$screen-sm-min}) {
    @content;
  }
}

@mixin maxSm {
  @media (max-width: #{$screen-sm-max}) {
    @content;
  }
}

// Medium devices
@mixin md {
  @media (min-width: #{$screen-md-min}) {
    @content;
  }
}

@mixin maxMd {
  @media (max-width: #{$screen-md-max}) {
    @content;
  }
}

@mixin maxMdSemi {
  @media (max-width: #{$screen-md-semi}) {
    @content;
  }
}

// Large devices

@mixin lg {
  @media (min-width: #{$screen-lg-min}) {
    @content;
  }
}

@mixin maxLgSemi {
  @media (max-width: #{$screen-lg-semi}) {
    @content;
  }
}

@mixin maxLg {
  @media (max-width: #{$screen-lg-max}) {
    @content;
  }
}

// Extra large devices
@mixin xl {
  @media (min-width: #{$screen-xl-min}) {
    @content;
  }
}

@mixin maxXlSemi {
  @media (max-width: #{$screen-xl-semi}) {
    @content;
  }
}

@mixin maxXl {
  @media (max-width: #{$screen-xl-max}) {
    @content;
  }
}

// Super Extra large devices
@mixin xxl {
  @media (min-width: #{$screen-xxl-min}) {
    @content;
  }
}

@mixin maxXxl {
  @media (max-width: #{$screen-xxl-max}) {
    @content;
  }
}

// version 2
$xs-max: 600px;
$sm-max: 768px;

@mixin maxSm2 {
  @media (max-width: #{$sm-max}) {
    @content;
  }
}
@mixin maxXs2 {
  @media (max-width: #{$xs-max}) {
    @content;
  }
}
