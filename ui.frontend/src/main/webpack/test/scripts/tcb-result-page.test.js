import $ from 'jquery';
import { TcbResultPage } from '../../site/scripts/tcb-result-page';

jest.mock('../../site/scripts/utils/get-data-result-page.util', () => ({
  getDataResultPage: jest.fn(),
}));

jest.mock('../../site/scripts/utils/location.util', () => ({
  LocationUtil: {
    getPageUrlParams: jest.fn(() => 'q=Test'),
    getUrlParamObj: jest.fn(() => ({ q: 'Test' })),
  },
}));

jest.mock('dompurify', () => ({
  sanitize: jest.fn((s) => s),
}));

customElements.define('tcb-result-page', TcbResultPage);

describe('TcbResultPage', () => {
  let instance;

  beforeEach(() => {
    document.body.innerHTML = `
    <tcb-result-page id="tcbPage"
      data-class-display-page="display-all-page"
      data-url="/api/promotions"
      data-lang="vi"
      data-day-text="ngày"
      data-label-expired="Còn"
      data-label-expired-count-down="Kết thúc"
      data-card-label="true"
      data-read-more="Xem thêm"
      data-search-count="%count ưu đãi"
      data-group-card-name="result-promotion-card"
      data-id-root="tcbPage"
    >
      <div class="tcb-result-page">
        <div class="result-promotion-card"></div>
        <div class="tcb-result-page-hero-notfound"></div>
        <div class="tcb-result-page-content"></div>
        <div class="promotion-total-count"></div>
        <div class="keywork-notfoud"></div>
        <div class="add-more-text"></div>
        <div class="button-more-promotion"><span></span></div>
      </div>
    </tcb-result-page>

    <div class="tcb-need-promotion-search">
      <div class="need-icon-remove-search"></div>
      <div class="need-promotions-filter"></div>
      <input class="need-search-input" />
    </div>

    <div class="display-result-page"></div>
    <div class="display-all-page" style="display: none;"></div>
  `;

    instance = document.querySelector('tcb-result-page');
    instance.connectedCallback(); // gọi lifecycle
  });

  afterEach(() => {
    jest.clearAllMocks();
    document.body.innerHTML = '';
  });

  test('should initialize and bind elements correctly', () => {
    expect(instance.resultNotfound).toBeInstanceOf(HTMLElement);
    expect(instance.searchTextInputElement).toBeInstanceOf(HTMLInputElement);
  });

  test('should read search param from URL and populate input', () => {
    const $input = $('.need-search-input');
    expect($input.val()).toBe('Test');
  });

  test('should build correct API endpoint', () => {
    instance.paramsValue.products = 'card1';
    instance.paramsValue.sort = 'latest';
    instance.paramsValue.searchText = 'phone';

    const url = new URL('http://localhost/?q=phone');
    instance.url = url;

    const endpoint = instance.getEndpoint();

    expect(endpoint).toContain('limit=6');
    expect(endpoint).toContain('offset=0');
    expect(endpoint).toContain('products=card1');
    expect(endpoint).toContain('sort=latest');
    expect(endpoint).toContain('searchText=phone');
  });

  test('should trigger search on clicking filter button', () => {
    const $button = $('.need-promotions-filter');
    const $container = $('<div></div>');
    instance.cardContainer = $container[0];
    instance.handleParamUrl = jest.fn();

    $button.trigger('click');

    expect(instance.handleParamUrl).toHaveBeenCalled();
  });

  test('should handle "Enter" keydown to trigger search', () => {
    const $input = $('.need-search-input');
    instance.cardContainer = $('<div></div>')[0];
    instance.handleParamUrl = jest.fn();

    const event = new KeyboardEvent('keydown', { keyCode: 13 });
    $input[0].dispatchEvent(event);

    expect(instance.handleParamUrl).toHaveBeenCalled();
  });

  test('should clear input and search on remove icon click', () => {
    const $input = $('.need-search-input');
    $input.val('abc');
    const $icon = $('.need-icon-remove-search');
    instance.cardContainer = $('<div></div>')[0];
    instance.handleParamUrl = jest.fn();

    $icon.trigger('click');

    expect($input.val()).toBe('');
    expect(instance.handleParamUrl).toHaveBeenCalled();
  });

  test('should call renderTcbCardNotFound when total is 0', () => {
    instance.renderTcbCardNotFound = jest.fn();
    instance.paramsValue.limit = 6;

    instance.handleCountMore(0);

    expect(instance.renderTcbCardNotFound).toHaveBeenCalled();
    expect($('.add-more-text').html()).toBe('');
  });

  test('should show "Xem thêm" when more results exist', () => {
    instance.paramsValue.offset = 0;
    instance.paramsValue.limit = 6;
    instance.nameAddMore = 'Xem thêm';

    instance.handleCountMore(20);

    expect($('.add-more-text').html()).toBe('Xem thêm');
    expect($('.promotion-total-count').html()).toContain('20');
  });
  test('handleParamUrl - isResultPage false with searchText', () => {
    instance.searchTextInputElement.value = 'phone';
    instance.isResultPage = false;
    instance.cardContainer = document.createElement('div');
    instance.addKeyWork = document.createElement('div');
    instance.handleTogglePage = jest.fn();
    instance.setURLParams = jest.fn();
    instance.getPromotions = jest.fn();
    instance.url = new URL('http://localhost/');

    instance.handleParamUrl();

    expect(instance.setURLParams).toHaveBeenCalledWith('q', 'phone', true);
    expect(instance.getPromotions).not.toHaveBeenCalled(); // vì return early
  });

  test('handleParamUrl - isResultPage true with no searchText', () => {
    instance.searchTextInputElement.value = '';
    instance.isResultPage = true;
    instance.cardContainer = document.createElement('div');
    instance.addKeyWork = document.createElement('div');
    instance.handleTogglePage = jest.fn();
    instance.removeURLParams = jest.fn();
    instance.getPromotions = jest.fn();
    instance.url = new URL('http://localhost/');

    instance.handleParamUrl();

    expect(instance.removeURLParams).toHaveBeenCalledWith('q', true);
    expect(instance.getPromotions).not.toHaveBeenCalled();
  });

  test('handleTogglePage - with no nameClassAllPage', () => {
    instance.nameClassAllPage = '';
    instance.textInputSearch = '';
    instance.resultNotfound = document.createElement('div');

    const parent = document.createElement('div');
    const addMoreResult = document.createElement('div');
    parent.appendChild(addMoreResult);
    instance.addMoreResult = addMoreResult;

    instance.handleTogglePage();

    expect(instance.resultNotfound.style.display).toBe('none');
    expect(parent.style.display).toBe('none');
  });

  test('handleTogglePage - empty searchText should show all page', () => {
    instance.nameClassAllPage = 'display-all-page';
    instance.textInputSearch = '';
    instance.toggleResultPage = document.createElement('div');
    instance.toggleAllPage = document.createElement('div');
    instance.resultNotfound = document.createElement('div');

    const parent = document.createElement('div');
    const addMoreResult = document.createElement('div');
    parent.appendChild(addMoreResult);
    instance.addMoreResult = addMoreResult;

    instance.handleTogglePage();

    expect(instance.toggleAllPage.style.display).toBe('block');
    expect(instance.toggleResultPage.style.display).toBe('none');
  });

  test('handleTogglePage - non-empty searchText should show result page', () => {
    instance.nameClassAllPage = 'display-all-page';
    instance.textInputSearch = 'abc';
    instance.toggleResultPage = document.createElement('div');
    instance.toggleAllPage = document.createElement('div');
    instance.resultNotfound = document.createElement('div');

    const parent = document.createElement('div');
    const addMoreResult = document.createElement('div');
    parent.appendChild(addMoreResult);
    instance.addMoreResult = addMoreResult;

    instance.handleTogglePage();

    expect(instance.toggleAllPage.style.display).toBe('none');
    expect(instance.toggleResultPage.style.display).toBe('block');
  });

  test('getEndpoint - with fallback values', () => {
    instance.paramsValue = {
      limit: 6,
      offset: 0,
      sort: 'latest',
      products: 'card1',
      memberships: 'vip',
      types: 'promo',
      searchText: 'apple',
      location: 'hanoi',
      cardTypes: 'gold',
    };
    instance.url = new URL('http://localhost/');
    instance.dataUrl = '/api/promo';

    const endpoint = instance.getEndpoint();
    expect(endpoint).toContain('limit=6');
    expect(endpoint).toContain('products=card1');
    expect(endpoint).toContain('sort=latest');
    expect(endpoint).toContain('searchText=apple');
  });

  test('renderTcbCardNotFound should show notfound and hide results', () => {
    const notfound = document.createElement('div');
    const resultPage = document.createElement('div');
    instance.resultNotfound = notfound;
    instance.resultPage = resultPage;

    instance.renderTcbCardNotFound();

    expect(notfound.style.display).toBe('block');
    expect(resultPage.style.display).toBe('none');
  });
});
