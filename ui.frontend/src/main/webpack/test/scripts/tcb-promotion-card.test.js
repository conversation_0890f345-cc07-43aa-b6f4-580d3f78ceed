jest.mock('../../site/scripts/utils/day-expired.util', () => ({
  renderDayExpired: jest.fn(),
}));

import { renderDayExpired } from '../../site/scripts/utils/day-expired.util';
import { TcbPromotionCard } from '../../site/scripts/tcb-promotion-card';

describe('TcbPromotionCard', () => {
  beforeEach(() => {
    if (!customElements.get('tcb-promotion-card')) {
      customElements.define('tcb-promotion-card', TcbPromotionCard);
    }
    const element = document.createElement('tcb-promotion-card');
    document.body.appendChild(element);
    jest.useFakeTimers();
    jest.clearAllTimers();
    jest.clearAllMocks();
  });

  afterEach(() => {
    jest.useRealTimers();
  });

  it('should call renderDayExpired on init', () => {
    new TcbPromotionCard();
    expect(renderDayExpired).toHaveBeenCalledTimes(1);
  });

  it('should call renderDayExpired at intervals', () => {
    new TcbPromotionCard();
    jest.advanceTimersByTime(3000);
    expect(renderDayExpired).toHaveBeenCalledTimes(4);
  });

  it('should clear interval on destroy', () => {
    const card = new TcbPromotionCard();
    const clearSpy = jest.spyOn(global, 'clearInterval');
    card.destroy();
    expect(clearSpy).toHaveBeenCalled();
  });

  it('should call renderDayExpired on forceRender', () => {
    const card = new TcbPromotionCard();
    renderDayExpired.mockClear();
    card.forceRender();
    expect(renderDayExpired).toHaveBeenCalledTimes(1);
  });
});
