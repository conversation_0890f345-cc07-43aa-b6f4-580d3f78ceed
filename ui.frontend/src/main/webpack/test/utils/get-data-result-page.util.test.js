import {
  renderViewCard,
  getDataResultPage,
  renderTcbCard,
  renderPromotionCard,
} from '../../site/scripts/utils/get-data-result-page.util';
import { fetchData } from '../../site/scripts/offer-helper';
import { TcbPromotionCard } from '../../site/scripts/tcb-promotion-card';
import DOMPurify from 'dompurify';

jest.mock('../../site/scripts/offer-helper', () => ({
  fetchData: jest.fn(),
}));

jest.mock('../../site/scripts/tcb-promotion-card', () => ({
  TcbPromotionCard: jest.fn(),
}));

jest.mock('dompurify', () => ({
  sanitize: jest.fn((html) => html),
}));

global.$ = require('jquery');

describe('get-data-result-page.util.js', () => {
  const mockDataExpiry = {
    lang: 'en',
    dayText: 'days',
    labelExpired: 'Expired',
    labelExpiredCountDown: 'Soon',
  };

  const mockParams = {
    dataUrl: '/mock-url',
    classGroupCardName: '.card-group',
    dataExpiryDate: mockDataExpiry,
    isCardlabel: true,
    totalPromotionData: jest.fn(),
  };

  beforeEach(() => {
    document.body.innerHTML = '';
    jest.clearAllMocks();
  });

  describe('renderViewCard', () => {
    it('should not throw if tcbElement does not have getPromotionsFilter', () => {
      const tcbElement = undefined;
      expect(() => renderViewCard('test', tcbElement)).not.toThrow();
    });

    it('should call getPromotionsFilter with urlParam', () => {
      const mockElement = {
        getPromotionsFilter: jest.fn(),
      };
      const urlParam = 'param123';
      renderViewCard(urlParam, mockElement);
      expect(mockElement.getPromotionsFilter).toHaveBeenCalledWith(urlParam);
    });

    it('should do nothing if tcbElement is null', () => {
      expect(() => renderViewCard('param123', null)).not.toThrow();
    });
  });

  describe('getDataResultPage', () => {
    it('should call fetchData and render cards when data is valid', async () => {
      const mockData = {
        total: 2,
        results: [
          {
            partner: ['Partner A'],
            products: ['Product A'],
            url: '/promo',
            thumbnail: '/img.jpg',
            description: 'Description A',
            expiryDate: '2025-06-01',
            favourite: 'true',
            isFavorite: true,
          },
        ],
      };

      fetchData.mockResolvedValue(mockData);
      const container = document.createElement('div');
      container.className = 'card-group new-goup-card';
      document.body.appendChild(container);

      await getDataResultPage(mockParams);

      expect(fetchData).toHaveBeenCalledWith('/mock-url');
      expect(mockParams.totalPromotionData).toHaveBeenCalledWith(2);
      expect(container.innerHTML).toContain('Partner A');
    });

    it('should not call totalPromotionData if it is not a function', async () => {
      fetchData.mockResolvedValue({ total: 3, results: [] });
      const params = {
        ...mockParams,
        totalPromotionData: null,
      };
      await getDataResultPage(params);
      expect(mockParams.totalPromotionData).not.toHaveBeenCalled();
    });

    it('should handle 0 promotions safely', async () => {
      fetchData.mockResolvedValue({ total: 0, results: [] });
      await getDataResultPage(mockParams);
      expect(mockParams.totalPromotionData).toHaveBeenCalledWith(0);
    });

    it('should not call renderTcbCard if results is empty', async () => {
      fetchData.mockResolvedValue({ total: 1, results: [] });
      await getDataResultPage(mockParams);
      expect(mockParams.totalPromotionData).toHaveBeenCalledWith(1);
    });
  });

  describe('renderTcbCard', () => {
    it('should append sanitized cards and initialize TcbPromotionCard', () => {
      const promotions = [
        {
          partner: ['Partner X'],
          products: ['Prod X'],
          url: '/promo-x',
          thumbnail: '/img-x.jpg',
          description: 'Desc X',
          expiryDate: '2025-07-01',
          favourite: 'true',
          isFavorite: true,
        },
      ];

      const container = document.createElement('div');
      container.className = 'card-group new-goup-card';
      document.body.appendChild(container);

      renderTcbCard(promotions, '.card-group', mockDataExpiry, true);

      expect(DOMPurify.sanitize).toHaveBeenCalled();
      expect(container.innerHTML).toContain('Partner X');
      expect(TcbPromotionCard).toHaveBeenCalled();
    });

    it('should render without new-card class if not in classList', () => {
      const cardEl = document.createElement('div');
      cardEl.className = 'card-group';
      document.body.appendChild(cardEl);

      const promo = [
        {
          partner: ['P1'],
          products: [],
          url: '/',
          thumbnail: '',
          description: '',
          expiryDate: '',
          favourite: 'false',
          isFavorite: false,
        },
      ];

      renderTcbCard(promo, '.card-group', {}, true);
      expect(cardEl.innerHTML).not.toContain('new-card');
    });

    it('should handle non-new-card group', () => {
      const promotions = [
        {
          partner: ['Partner Y'],
          products: ['Prod Y'],
          url: '/promo-y',
          thumbnail: '/img-y.jpg',
          description: 'Desc Y',
          expiryDate: '2025-07-02',
          favourite: 'false',
          isFavorite: false,
        },
      ];

      const container = document.createElement('div');
      container.className = 'card-group';
      document.body.appendChild(container);

      renderTcbCard(promotions, '.card-group', mockDataExpiry, false);

      expect(container.innerHTML).toContain('Partner Y');
      expect(container.innerHTML).not.toContain('new-card');
    });

    it('should handle empty promotions array', () => {
      const container = document.createElement('div');
      container.className = 'card-group';
      document.body.appendChild(container);

      renderTcbCard([], '.card-group', {}, false);

      expect(container.innerHTML).toBe('');
      expect(TcbPromotionCard).toHaveBeenCalled();
    });
  });

  describe('renderPromotionCard', () => {
    it('should render full card with label and newCard', () => {
      const item = {
        partner: ['Teco'],
        products: ['Banking'],
        url: '/promo-teco',
        thumbnail: '/img.jpg',
        description: 'Bank promo',
        expiryDate: '2025-07-01',
        favourite: 'true',
        isFavorite: true,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).toContain('Teco');
      expect(html).toContain('Banking');
      expect(html).toContain('new-card');
      expect(html).toContain('data-promotion-card-expired="2025-07-01"');
    });

    it('should render card without label and not newCard', () => {
      const item = {
        partner: ['SimpleCo'],
        products: [],
        url: '/promo-simple',
        thumbnail: '',
        description: '',
        expiryDate: '',
        favourite: 'false',
        isFavorite: false,
      };

      const html = renderPromotionCard(item, false, false, {});
      expect(html).toContain('SimpleCo');
      expect(html).not.toContain('card__label');
      expect(html).not.toContain('new-card');
    });

    it('should render without product labels when products array is empty', () => {
      const item = {
        partner: ['Partner'],
        products: [],
        url: '/promo',
        thumbnail: '/img.jpg',
        description: 'Desc',
        expiryDate: '2025-01-01',
        favourite: 'false',
        isFavorite: false,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).not.toContain('group-card__label-mobile');
      expect(html).toContain('group-card__label');
    });

    it('should handle favourite="true" but isFavorite=false', () => {
      const item = {
        partner: ['Partner'],
        products: ['Product'],
        url: '/promo',
        thumbnail: '/img.jpg',
        description: 'Desc',
        expiryDate: '2025-01-01',
        favourite: 'true',
        isFavorite: false,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).toContain('display-none');
    });

    it('should handle missing thumbnail', () => {
      const item = {
        partner: ['Partner'],
        products: ['Product'],
        url: '/promo',
        thumbnail: undefined,
        description: 'Desc',
        expiryDate: '2025-01-01',
        favourite: 'true',
        isFavorite: true,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).toContain('src=""');
    });

    it('should handle missing partner', () => {
      const item = {
        partner: [],
        products: ['Product'],
        url: '/promo',
        thumbnail: '/img.jpg',
        description: 'Desc',
        expiryDate: '2025-01-01',
        favourite: 'true',
        isFavorite: true,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).toContain('alt=""');
      expect(html).toContain('card__title"></div>');
    });

    it('should handle missing expiryDate', () => {
      const item = {
        partner: ['Partner'],
        products: ['Product'],
        url: '/promo',
        thumbnail: '/img.jpg',
        description: 'Desc',
        expiryDate: '',
        favourite: 'true',
        isFavorite: true,
      };

      const html = renderPromotionCard(item, true, true, mockDataExpiry);
      expect(html).toContain('data-promotion-card-expired=""');
    });
  });
});
